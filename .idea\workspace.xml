<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d993c3fb-e064-47f3-8d85-f88278d05388" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/data/prod-html/analysis/analysis-services.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/analysis/hdinsight.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/analysis/stream-analytics.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/compute/azure-functions.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/database/data-explorer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/identity/active-directory-ds.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/iot/event-hubs.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/iot/iot-edge.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/iot/iot-hub.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/prod-html/networking/application-gateway.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cli.py" beforeDir="false" afterPath="$PROJECT_DIR$/cli.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/configs/products-index.json" beforeDir="false" afterPath="$PROJECT_DIR$/data/configs/products-index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/configs/products/compute/microsoft-entra-external-id.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/data/configs/products/integration/api-management.json" beforeDir="false" afterPath="$PROJECT_DIR$/data/configs/products/integration/api-management.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/configs/soft-category.json" beforeDir="false" afterPath="$PROJECT_DIR$/data/configs/soft-category.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/anomaly-detector-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/ai/cognitive-services/anomaly-detector.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/api-management-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/integration/api-management.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/batch-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/compute/batch.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/cosmos-db-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/database/cosmos-db.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/databox-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/storage/databox-index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/databricks-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/ai/databricks.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/event-grid-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/integration/event-grid.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/machine-learning-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/ai/machine-learning.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/mariadb-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/database/mariadb.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/microsoft-entra-external-id-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/identity/active-directory-b2c.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/multi-factor-authentication-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/identity/multi-factor-authentication.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/mysql-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/database/mysql.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/postgresql-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/database/postgresql.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/power-bi-embedded-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/analysis/power-bi-embedded.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/search-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/ai/search.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/service-bus-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/integration/service-bus.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/sql-database-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/database/sql-database.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/ssis-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/analysis/data-factory/ssis.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/storage_data-lake_index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/storage/storage/storage_data-lake_index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/storage_files_index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/storage/storage/storage_files_index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/storage_page-blobs_index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/storage/storage/storage_page-blobs_index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/data/prod-html/vpn-gateway-index.html" beforeDir="false" afterPath="$PROJECT_DIR$/data/prod-html/networking/vpn-gateway.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pyproject.toml" beforeDir="false" afterPath="$PROJECT_DIR$/pyproject.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/config_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/config_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/extraction_coordinator.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/extraction_coordinator.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/product_manager.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/product_manager.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/core/region_processor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/core/region_processor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/extractors/enhanced_cms_extractor.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/extractors/enhanced_cms_extractor.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/extractors/enhanced_cms_extractor_backup.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/strategies/base_strategy.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/strategies/base_strategy.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/strategies/region_filter_strategy.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/strategies/region_filter_strategy.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/content/content_utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/content/content_utils.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="30wv000xF0FdL29qZAIP33k97XJ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "refactor-core",
    "last_opened_file_path": "D:/Playground/AzureCNArchaeologist",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d993c3fb-e064-47f3-8d85-f88278d05388" name="Changes" comment="" />
      <created>1754551223306</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754551223306</updated>
      <workItem from="1754551224408" duration="3440000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>