2025-08-07 16:53:32.104 | INFO     | src.core.logging:setup_logging:106 | 日志系统已初始化，日志级别: INFO
2025-08-07 16:53:32.104 | INFO     | src.core.logging:setup_logging:107 | 日志目录: D:\Playground\AzureCNArchaeologist\logs
2025-08-07 16:53:32.104 | INFO     | __main__:main:324 | AzureCN Archaeologist CLI 启动
2025-08-07 16:53:32.104 | INFO     | __main__:main:344 | 执行命令: extract
2025-08-07 16:53:32.104 | INFO     | __main__:extract_command:54 | 开始提取产品数据: api-management
2025-08-07 16:53:32.104 | INFO     | __main__:extract_command:55 | HTML文件: data/prod-html/integration/api-management.html
2025-08-07 16:53:32.104 | INFO     | __main__:extract_command:56 | 输出格式: json
2025-08-07 16:53:32.104 | INFO     | __main__:extract_command:57 | 输出目录: output/api-management
2025-08-07 16:53:32.104 | INFO     | src.core.product_manager:__init__:34 | 产品管理器初始化完成
2025-08-07 16:53:32.104 | INFO     | src.core.product_manager:__init__:35 | 配置目录: data\configs
2025-08-07 16:53:32.104 | INFO     | src.core.product_manager:load_products_index:51 | 加载产品索引: 11 个产品
2025-08-07 16:53:32.152 | INFO     | src.core.product_manager:__init__:34 | 产品管理器初始化完成
2025-08-07 16:53:32.152 | INFO     | src.core.product_manager:__init__:35 | 配置目录: data\configs
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:__init__:47 | 提取协调器初始化完成
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:__init__:48 | 输出目录: output\api-management
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:_validate_strategy_setup:345 | 验证策略设置...
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:_validate_strategy_setup:352 | 策略注册状态: 1/6 (16.7%)
2025-08-07 16:53:32.152 | WARNING  | src.core.extraction_coordinator:_validate_strategy_setup:356 | 缺少策略: ['simple_static', 'tab', 'region_tab', 'multi_filter', 'large_file']
2025-08-07 16:53:32.152 | INFO     | src.extractors.enhanced_cms_extractor:__init__:42 | 增强型CMS提取器初始化完成
2025-08-07 16:53:32.152 | INFO     | src.extractors.enhanced_cms_extractor:__init__:43 | 输出目录: output/api-management
2025-08-07 16:53:32.152 | INFO     | src.extractors.enhanced_cms_extractor:extract_cms_content:56 | 开始提取增强型CMS内容
2025-08-07 16:53:32.152 | INFO     | src.extractors.enhanced_cms_extractor:extract_cms_content:57 | 源文件: data/prod-html/integration/api-management.html
2025-08-07 16:53:32.152 | INFO     | src.extractors.enhanced_cms_extractor:extract_cms_content:74 | 委托给提取协调器处理...
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:coordinate_extraction:67 | 开始协调提取流程
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:coordinate_extraction:68 | 源文件: data/prod-html/integration/api-management.html
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:coordinate_extraction:69 | 源URL: https://www.azure.cn/pricing/details/api-management/
2025-08-07 16:53:32.152 | INFO     | src.core.product_manager:load_products_index:51 | 加载产品索引: 11 个产品
2025-08-07 16:53:32.152 | INFO     | src.core.product_manager:detect_product_from_filename:137 | 检测到产品: api-management.html -> api-management
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:coordinate_extraction:74 | 检测到产品: api-management
2025-08-07 16:53:32.152 | INFO     | src.core.extraction_coordinator:_get_product_config:144 | 获取产品配置: 11 个配置项
2025-08-07 16:53:32.201 | INFO     | src.core.extraction_coordinator:_determine_extraction_strategy:168 | 策略决策成功: region_filter
2025-08-07 16:53:32.201 | INFO     | src.core.extraction_coordinator:coordinate_extraction:81 | 选择策略: region_filter
2025-08-07 16:53:32.201 | INFO     | src.core.extraction_coordinator:coordinate_extraction:82 | 处理器: RegionFilterProcessor
2025-08-07 16:53:32.201 | INFO     | src.core.region_processor:_load_region_config:38 | 加载区域配置: 96 个配置项，转换为 22 个产品
2025-08-07 16:53:32.201 | INFO     | src.core.region_processor:__init__:25 | ✓ 区域处理器初始化完成
2025-08-07 16:53:32.201 | INFO     | src.core.region_processor:__init__:26 | 📁 区域配置文件: data/configs/soft-category.json
2025-08-07 16:53:32.201 | INFO     | src.core.extraction_coordinator:_create_strategy_instance:201 | 策略实例创建成功: RegionFilterStrategy
2025-08-07 16:53:32.201 | INFO     | src.core.extraction_coordinator:_prepare_html_content:221 | 读取和解析HTML文件...
2025-08-07 16:53:32.216 | INFO     | src.core.extraction_coordinator:_prepare_html_content:246 | HTML解析完成: 32892 字符
2025-08-07 16:53:32.216 | INFO     | src.core.extraction_coordinator:_execute_extraction:261 | 执行提取策略: RegionFilterStrategy
2025-08-07 16:53:32.247 | INFO     | src.core.region_processor:detect_available_regions:97 | 检测可用区域...
2025-08-07 16:53:32.247 | INFO     | src.core.region_processor:detect_available_regions:139 | 检测到 5 个区域: ['east-china', 'east-china2', 'north-china', 'north-china2', 'north-china3']
2025-08-07 16:53:32.247 | INFO     | src.core.region_processor:extract_region_contents:162 | 处理区域: east-china
2025-08-07 16:53:32.247 | INFO     | src.core.region_processor:apply_region_filtering:183 | 应用区域筛选: east-china
2025-08-07 16:53:32.267 | INFO     | src.core.region_processor:extract_region_contents:162 | 处理区域: east-china2
2025-08-07 16:53:32.267 | INFO     | src.core.region_processor:apply_region_filtering:183 | 应用区域筛选: east-china2
2025-08-07 16:53:32.285 | INFO     | src.core.region_processor:extract_region_contents:162 | 处理区域: north-china
2025-08-07 16:53:32.285 | INFO     | src.core.region_processor:apply_region_filtering:183 | 应用区域筛选: north-china
2025-08-07 16:53:32.294 | INFO     | src.core.region_processor:extract_region_contents:162 | 处理区域: north-china2
2025-08-07 16:53:32.294 | INFO     | src.core.region_processor:apply_region_filtering:183 | 应用区域筛选: north-china2
2025-08-07 16:53:32.310 | INFO     | src.core.region_processor:extract_region_contents:162 | 处理区域: north-china3
2025-08-07 16:53:32.310 | INFO     | src.core.region_processor:apply_region_filtering:183 | 应用区域筛选: north-china3
2025-08-07 16:53:32.326 | INFO     | src.core.region_processor:extract_region_contents:177 | 成功提取 5 个区域的内容
2025-08-07 16:53:32.326 | INFO     | src.core.extraction_coordinator:_execute_extraction:265 | 策略提取完成
2025-08-07 16:53:32.326 | INFO     | src.core.extraction_coordinator:_post_process_and_validate:285 | 后处理和验证...
2025-08-07 16:53:32.326 | INFO     | src.core.extraction_coordinator:_post_process_and_validate:303 | 数据验证完成: 有效
2025-08-07 16:53:32.326 | INFO     | src.core.extraction_coordinator:coordinate_extraction:100 | 提取流程完成
2025-08-07 16:53:32.326 | INFO     | src.extractors.enhanced_cms_extractor:extract_cms_content:88 | 提取完成
2025-08-07 16:53:32.326 | INFO     | __main__:extract_command:112 | 数据已导出到: output\api-management\api-management_enhanced_cms_content_20250807_165332.json
2025-08-07 16:53:32.326 | INFO     | __main__:extract_command:162 | 数据提取完成
2025-08-07 16:53:32.326 | INFO     | __main__:main:351 | 命令 extract 执行完成
