"""
Strategies module for extraction strategy implementations.

This module contains different extraction strategies for various page types:
- BaseStrategy: Abstract base strategy
- RegionFilterStrategy: For pages with region filtering (implemented)
- SimpleStaticStrategy: For simple static pages (planned)
- TabStrategy: For tab-controlled pages (planned)
- RegionTabStrategy: For combined region+tab pages (planned)
- MultiFilterStrategy: For multi-filter pages (planned)
- LargeFileStrategy: For large file optimization (planned)
"""

# Import base classes
from .base_strategy import BaseStrategy
from .strategy_factory import StrategyFactory

# Import data models for proper typing
from src.core.data_models import StrategyType

# Import implemented strategies
from .region_filter_strategy import RegionFilterStrategy

# Register implemented strategies
print("📋 注册策略到StrategyFactory...")

try:
    StrategyFactory.register_strategy(StrategyType.REGION_FILTER, RegionFilterStrategy)
    print("✅ RegionFilterStrategy 已注册")
except Exception as e:
    print(f"⚠️ RegionFilterStrategy 注册失败: {e}")

# Strategy registry for tracking (using enum as key for consistency)
STRATEGY_REGISTRY = {
    StrategyType.REGION_FILTER: RegionFilterStrategy,
    # More strategies will be added as they are implemented
}

print(f"📊 已注册策略数量: {len(STRATEGY_REGISTRY)}")

# Export main classes and factory
__all__ = [
    'BaseStrategy',
    'StrategyFactory', 
    'RegionFilterStrategy',
    'STRATEGY_REGISTRY'
]